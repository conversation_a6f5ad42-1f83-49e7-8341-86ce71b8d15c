package com.armamc.terrenobuyer;

import com.armamc.terrenobuyer.modules.TerrenoBuyerModule;
import meteordevelopment.meteorclient.addons.MeteorAddon;
import meteordevelopment.meteorclient.systems.modules.Category;
import meteordevelopment.meteorclient.systems.modules.Modules;

public class TerrenoBuyerAddon extends MeteorAddon {
    public static final Category CATEGORY = new Category("Terreno Buyer");
    
    @Override
    public void onInitialize() {
        TerrenoBuyerClient.LOGGER.info("Inicializando Terreno Buyer Addon para Meteor Client");
        
        // Registrar módulos
        Modules.get().add(new TerrenoBuyerModule());
    }
    
    @Override
    public String getPackage() {
        return "com.armamc.terrenobuyer";
    }
    
    @Override
    public void onRegisterCategories() {
        Modules.registerCategory(CATEGORY);
    }
}
