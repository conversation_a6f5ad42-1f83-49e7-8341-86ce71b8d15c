# Terreno Buyer - Mod para Minecraft

Um mod para Minecraft Fabric que automatiza a compra de terrenos no servidor armamc.com, integrado com o Meteor Client.

## Funcionalidades

- **Detecção Automática**: Detecta automaticamente quando você clica em uma proteção e captura o número da proteção
- **Auto-clique**: Continua clicando automaticamente até que a opção de compra apareça
- **Compra Automática**: Executa automaticamente o comando `/imob comprar [número]` quando a opção de compra aparece
- **Integração com Meteor Client**: Interface completa no Meteor Client com configurações personalizáveis

## Como Funciona

1. **Clique na Proteção**: Quando você clica com a vareta em uma proteção, o mod detecta a mensagem "Proteção #[número]"
2. **Auto-clique Ativo**: O mod automaticamente continua clicando com o botão direito para verificar se a proteção está disponível
3. **Detecção de Compra**: Quando aparece a mensagem "[Comprar Proteção]" no chat, o mod executa automaticamente o comando de compra
4. **Compra Automática**: O comando `/imob comprar [número]` é executado automaticamente

## Instalação

### Pré-requisitos
- Minecraft 1.20.4
- Fabric Loader 0.15.3+
- Fabric API 0.91.2+
- Meteor Client 0.5.5+

### Passos de Instalação

1. **Clone o repositório**:
   ```bash
   git clone https://github.com/armamc/terreno-buyer.git
   cd terreno-buyer
   ```

2. **Compile o mod**:
   ```bash
   ./gradlew build
   ```

3. **Instale o mod**:
   - Copie o arquivo `.jar` da pasta `build/libs/` para a pasta `mods` do seu Minecraft
   - Certifique-se de que o Meteor Client também está instalado

## Configurações no Meteor Client

Após instalar o mod, você encontrará uma nova categoria "Terreno Buyer" no Meteor Client com as seguintes opções:

### Configurações Disponíveis

- **Auto-click**: Ativa/desativa o clique automático
- **Click Delay**: Define o intervalo entre cliques (em ticks, 20 ticks = 1 segundo)
- **Auto Buy**: Ativa/desativa a compra automática
- **Notifications**: Ativa/desativa notificações no chat

### Como Usar

1. Abra o Meteor Client (padrão: tecla `Right Shift`)
2. Navegue até a categoria "Terreno Buyer"
3. Ative o módulo "Terreno Buyer"
4. Configure as opções conforme sua preferência
5. Vá até uma proteção no servidor armamc.com
6. Clique na proteção com a vareta
7. O mod fará o resto automaticamente!

## Comandos e Funcionalidades

### Detecção de Mensagens
- **Proteção detectada**: `Proteção #[número]` - O mod captura o número da proteção
- **Opção de compra**: `[Comprar Proteção]` - Trigger para compra automática

### Comando Executado
- **Compra**: `/imob comprar [número]` - Comando executado automaticamente

## Desenvolvimento

### Estrutura do Projeto
```
src/main/java/com/armamc/terrenobuyer/
├── TerrenoBuyerClient.java      # Cliente principal do mod
├── TerrenoBuyerAddon.java       # Integração com Meteor Client
├── TerrenoManager.java          # Lógica principal de gerenciamento
├── modules/
│   └── TerrenoBuyerModule.java  # Módulo do Meteor Client
└── mixins/
    └── ChatHudMixin.java        # Interceptação de mensagens do chat
```

### Compilação
```bash
./gradlew build
```

### Desenvolvimento
```bash
./gradlew runClient
```

## Licença

Este projeto está licenciado sob a licença MIT. Veja o arquivo LICENSE para mais detalhes.

## Contribuição

Contribuições são bem-vindas! Por favor, abra uma issue ou pull request para sugestões e melhorias.

## Aviso Legal

Este mod é destinado apenas para uso no servidor armamc.com. Certifique-se de que o uso de mods é permitido no servidor antes de usar.
