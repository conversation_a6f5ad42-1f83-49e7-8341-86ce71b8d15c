# Guia de Teste - Terreno Buyer Mod

## Preparação para Teste

### 1. Ambiente de Desenvolvimento
```bash
# Compilar o mod
./gradlew build

# Executar cliente de teste (opcional)
./gradlew runClient
```

### 2. Instalação para Teste
1. Copie `build/libs/terreno-buyer-1.0.0.jar` para a pasta mods
2. Certifique-se de ter Fabric API e Meteor Client instalados
3. Inicie o Minecraft com perfil Fabric 1.20.4

## Testes Funcionais

### Teste 1: Inicialização do Mod
**Objetivo**: Verificar se o mod carrega corretamente

**Passos**:
1. Inicie o Minecraft
2. Verifique os logs para: `[INFO] Terreno Buyer mod inicializado!`
3. Abra o Meteor Client (Right Shift)
4. Procure pela categoria "Terreno Buyer"
5. Verifique se o módulo "Terreno Buyer" está disponível

**Resultado E<PERSON>ado**: 
- ✅ Mod aparece no Meteor Client
- ✅ Logs mostram inicialização bem-sucedida

### Teste 2: Detecção de Proteção
**Objetivo**: Verificar se o mod detecta mensagens de proteção

**Passos**:
1. Entre no servidor armamc.com
2. Ative o módulo "Terreno Buyer" no Meteor Client
3. Pegue uma vareta (`/wand`)
4. Clique em uma proteção
5. Observe o chat para mensagem de detecção

**Resultado Esperado**:
- ✅ Mensagem: `[Terreno Buyer] Proteção #[número] detectada...`
- ✅ Auto-clique inicia automaticamente

### Teste 3: Auto-clique
**Objetivo**: Verificar se o auto-clique funciona

**Passos**:
1. Após detectar uma proteção (Teste 2)
2. Observe se o personagem clica automaticamente
3. Verifique se o intervalo de clique respeita a configuração
4. Teste alterar o "Click Delay" no Meteor Client

**Resultado Esperado**:
- ✅ Cliques automáticos visíveis
- ✅ Intervalo configurável funciona

### Teste 4: Detecção de Compra
**Objetivo**: Verificar se detecta opção de compra

**Preparação**: Use uma proteção que você sabe que está disponível

**Passos**:
1. Execute Teste 2 e 3
2. Aguarde até aparecer `[Comprar Proteção]` no chat
3. Observe se o mod executa o comando automaticamente

**Resultado Esperado**:
- ✅ Comando `/imob comprar [número]` executado
- ✅ Auto-clique para após compra
- ✅ Mensagem de confirmação no chat

### Teste 5: Configurações do Meteor Client
**Objetivo**: Verificar se todas as configurações funcionam

**Passos**:
1. Abra Meteor Client → Terreno Buyer
2. Teste cada configuração:
   - Desative "Auto-click" → Verificar se para de clicar
   - Altere "Click Delay" → Verificar mudança no intervalo
   - Desative "Auto Buy" → Verificar se não compra automaticamente
   - Desative "Notifications" → Verificar se para mensagens

**Resultado Esperado**:
- ✅ Todas as configurações afetam o comportamento
- ✅ Interface responsiva

## Testes de Integração

### Teste 6: Múltiplas Proteções
**Objetivo**: Testar comportamento com várias proteções

**Passos**:
1. Compre uma proteção (Teste 4)
2. Vá para outra proteção
3. Clique na nova proteção
4. Verifique se o mod reseta e detecta a nova

**Resultado Esperado**:
- ✅ Mod reseta após cada compra
- ✅ Detecta novas proteções corretamente

### Teste 7: Proteções Indisponíveis
**Objetivo**: Testar comportamento com proteções não disponíveis

**Passos**:
1. Clique em uma proteção já comprada/indisponível
2. Observe se o auto-clique continua indefinidamente
3. Teste desativar manualmente o módulo

**Resultado Esperado**:
- ✅ Auto-clique continua até ser parado
- ✅ Pode ser parado manualmente

## Testes de Robustez

### Teste 8: Reconexão ao Servidor
**Objetivo**: Verificar comportamento ao reconectar

**Passos**:
1. Ative o mod
2. Desconecte do servidor
3. Reconecte ao servidor
4. Verifique se o mod ainda funciona

**Resultado Esperado**:
- ✅ Mod reseta ao desconectar
- ✅ Funciona normalmente após reconectar

### Teste 9: Mensagens com Formatação
**Objetivo**: Testar detecção com códigos de cor do Minecraft

**Passos**:
1. Teste com mensagens que contenham códigos de cor (§)
2. Verifique se a detecção ainda funciona

**Resultado Esperado**:
- ✅ Detecta proteções mesmo com formatação
- ✅ Detecta opções de compra com formatação

### Teste 10: Performance
**Objetivo**: Verificar impacto na performance

**Passos**:
1. Ative o mod
2. Monitore FPS e uso de CPU
3. Deixe o auto-clique ativo por 5 minutos
4. Compare com mod desativado

**Resultado Esperado**:
- ✅ Impacto mínimo na performance
- ✅ Sem vazamentos de memória

## Checklist de Teste Completo

### Funcionalidades Básicas
- [ ] Mod carrega sem erros
- [ ] Aparece no Meteor Client
- [ ] Detecta mensagens de proteção
- [ ] Auto-clique funciona
- [ ] Detecta opções de compra
- [ ] Executa comando de compra
- [ ] Para após compra bem-sucedida

### Configurações
- [ ] Auto-click liga/desliga
- [ ] Click Delay configurável
- [ ] Auto Buy liga/desliga
- [ ] Notifications liga/desliga

### Robustez
- [ ] Funciona após reconexão
- [ ] Lida com formatação de texto
- [ ] Performance aceitável
- [ ] Não trava o jogo

### Integração
- [ ] Compatível com Meteor Client
- [ ] Compatível com Fabric API
- [ ] Funciona no servidor armamc.com

## Relatório de Bugs

### Como Reportar Bugs
1. **Reproduza o bug** seguindo os passos de teste
2. **Colete informações**:
   - Versão do Minecraft
   - Versão do Fabric
   - Versão do Meteor Client
   - Logs do console
   - Screenshots/vídeos se aplicável

3. **Descreva o problema**:
   - O que deveria acontecer
   - O que realmente aconteceu
   - Passos para reproduzir

### Bugs Conhecidos (Exemplo)
- ❌ **Bug #1**: Auto-clique não para se o jogador sair do servidor
  - **Solução**: Adicionar listener de desconexão
- ❌ **Bug #2**: Detecta proteções em outros servidores
  - **Solução**: Verificar IP do servidor

## Melhorias Futuras

### Funcionalidades Sugeridas
- [ ] Filtro por valor máximo de proteção
- [ ] Lista de proteções favoritas
- [ ] Histórico de compras
- [ ] Integração com economia do servidor
- [ ] Notificações visuais (não apenas chat)
- [ ] Suporte a outros comandos de proteção

### Otimizações
- [ ] Cache de proteções já verificadas
- [ ] Algoritmo de clique mais inteligente
- [ ] Redução do uso de CPU
- [ ] Melhor tratamento de erros
