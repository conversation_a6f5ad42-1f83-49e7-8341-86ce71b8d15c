package com.armamc.terrenobuyer.modules;

import com.armamc.terrenobuyer.TerrenoBuyerAddon;
import com.armamc.terrenobuyer.TerrenoBuyerClient;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.orbit.EventHandler;

public class TerrenoBuyerModule extends Module {
    
    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    
    private final Setting<Boolean> autoClick = sgGeneral.add(new BoolSetting.Builder()
        .name("auto-click")
        .description("Automaticamente clica com botão direito para verificar proteções")
        .defaultValue(true)
        .build()
    );
    
    private final Setting<Integer> clickDelay = sgGeneral.add(new IntSetting.Builder()
        .name("click-delay")
        .description("Delay entre cliques em ticks (20 ticks = 1 segundo)")
        .defaultValue(20)
        .min(1)
        .max(100)
        .sliderMax(100)
        .build()
    );
    
    private final Setting<Boolean> autoBuy = sgGeneral.add(new BoolSetting.Builder()
        .name("auto-buy")
        .description("Automaticamente compra proteções quando disponível")
        .defaultValue(true)
        .build()
    );
    
    private final Setting<Boolean> notifications = sgGeneral.add(new BoolSetting.Builder()
        .name("notifications")
        .description("Mostra notificações no chat")
        .defaultValue(true)
        .build()
    );
    
    public TerrenoBuyerModule() {
        super(TerrenoBuyerAddon.CATEGORY, "terreno-buyer", "Automatiza a compra de terrenos no armamc.com");
    }
    
    @Override
    public void onActivate() {
        if (notifications.get()) {
            info("Terreno Buyer ativado!");
        }
    }
    
    @Override
    public void onDeactivate() {
        if (TerrenoBuyerClient.getInstance() != null) {
            TerrenoBuyerClient.getInstance().getTerrenoManager().stopAutoClick();
        }
        
        if (notifications.get()) {
            info("Terreno Buyer desativado!");
        }
    }
    
    @EventHandler
    private void onTick(TickEvent.Pre event) {
        if (!isActive()) return;
        
        if (TerrenoBuyerClient.getInstance() != null) {
            // Aqui podemos adicionar lógica adicional se necessário
            // O TerrenoManager já tem seu próprio sistema de tick
        }
    }
    
    public boolean shouldAutoClick() {
        return isActive() && autoClick.get();
    }
    
    public int getClickDelay() {
        return clickDelay.get();
    }
    
    public boolean shouldAutoBuy() {
        return isActive() && autoBuy.get();
    }
    
    public boolean shouldShowNotifications() {
        return notifications.get();
    }
}
