# Guia de Instalação - Terreno Buyer Mod

## Pré-requisitos

### 1. Java Development Kit (JDK) 17+

**Opção A: Oracle JDK**
1. Baixe o JDK 17+ do site oficial: https://www.oracle.com/java/technologies/downloads/
2. Execute o instalador e siga as instruções
3. Adicione o Java ao PATH do sistema

**Opção B: OpenJDK (Recomendado)**
1. Baixe o OpenJDK 17+ de: https://adoptium.net/
2. Execute o instalador
3. Certifique-se de marcar "Add to PATH" durante a instalação

**Verificar Instalação:**
```bash
java -version
javac -version
```

### 2. Minecraft com Fabric

1. **Minecraft Java Edition** (versão 1.20.4)
2. **Fabric <PERSON>ader**: Baixe do site oficial https://fabricmc.net/use/installer/
3. **Fabric API**: Baixe da página do mod no CurseForge ou Modrinth

### 3. Meteor Client

1. Baixe o Meteor Client da página oficial: https://meteorclient.com/
2. Coloque o arquivo `.jar` na pasta `mods` do Minecraft

## Compilação do Mod

### Passo 1: Preparar o Ambiente

1. **Clone ou baixe este projeto**
2. **Abra um terminal/prompt de comando** na pasta do projeto
3. **Verifique se o Java está funcionando**:
   ```bash
   java -version
   ```

### Passo 2: Compilar

**No Windows:**
```bash
gradlew.bat build
```

**No Linux/Mac:**
```bash
./gradlew build
```

### Passo 3: Localizar o Arquivo Compilado

Após a compilação bem-sucedida, o arquivo `.jar` estará em:
```
build/libs/terreno-buyer-1.0.0.jar
```

## Instalação no Minecraft

### Passo 1: Localizar a Pasta Mods

**Windows:**
```
%APPDATA%\.minecraft\mods\
```

**Linux:**
```
~/.minecraft/mods/
```

**Mac:**
```
~/Library/Application Support/minecraft/mods/
```

### Passo 2: Copiar o Mod

1. Copie o arquivo `terreno-buyer-1.0.0.jar` para a pasta `mods`
2. Certifique-se de que os seguintes mods também estão na pasta:
   - `fabric-api-[versão].jar`
   - `meteor-client-[versão].jar`

### Passo 3: Iniciar o Minecraft

1. Abra o Minecraft Launcher
2. Selecione o perfil Fabric 1.20.4
3. Inicie o jogo

## Configuração e Uso

### Primeira Configuração

1. **Entre no servidor armamc.com**
2. **Abra o Meteor Client** (tecla padrão: `Right Shift`)
3. **Navegue até "Terreno Buyer"** na lista de categorias
4. **Ative o módulo "Terreno Buyer"**

### Configurações Recomendadas

- **Auto-click**: ✅ Ativado
- **Click Delay**: `20` (1 segundo)
- **Auto Buy**: ✅ Ativado
- **Notifications**: ✅ Ativado

### Como Usar

1. **Pegue uma vareta** no servidor
2. **Vá até uma proteção** que você quer comprar
3. **Clique na proteção** com a vareta
4. **O mod fará o resto automaticamente!**

## Solução de Problemas

### Erro: "Java não encontrado"
- Instale o JDK 17+ e adicione ao PATH do sistema
- Reinicie o terminal/prompt de comando

### Erro: "Fabric API não encontrada"
- Baixe e instale a Fabric API na pasta mods
- Certifique-se de que a versão é compatível (1.20.4)

### Erro: "Meteor Client não encontrado"
- Baixe e instale o Meteor Client na pasta mods
- Verifique se a versão é compatível

### Mod não aparece no Meteor Client
- Verifique se todos os arquivos estão na pasta mods correta
- Reinicie o Minecraft completamente
- Verifique os logs do Minecraft para erros

### Auto-clique não funciona
- Certifique-se de que o módulo está ativado no Meteor Client
- Verifique se você está no servidor armamc.com
- Teste clicando manualmente primeiro para ver se detecta a proteção

## Logs e Debug

Para verificar se o mod está funcionando:

1. **Abra o console do Minecraft** (F3 + D para mostrar logs)
2. **Procure por mensagens** que começam com `[Terreno Buyer]`
3. **Verifique se aparece**: "Terreno Buyer mod inicializado!"

## Suporte

Se você encontrar problemas:

1. **Verifique os logs** do Minecraft
2. **Teste em um mundo single-player** primeiro
3. **Certifique-se** de que todas as dependências estão instaladas
4. **Abra uma issue** no repositório do projeto com detalhes do erro
