@echo off
echo ========================================
echo    Terreno Buyer Mod - Build Script
echo ========================================
echo.

REM Verificar se Java está instalado
echo Verificando instalação do Java...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERRO] Java não encontrado!
    echo.
    echo Por favor, instale o Java JDK 17+ antes de continuar.
    echo Download: https://adoptium.net/
    echo.
    echo Após instalar, certifique-se de que o Java está no PATH do sistema.
    pause
    exit /b 1
)

echo [OK] Java encontrado!
java -version
echo.

REM Verificar se gradlew existe
if not exist "gradlew.bat" (
    echo [ERRO] gradlew.bat não encontrado!
    echo Certifique-se de estar na pasta correta do projeto.
    pause
    exit /b 1
)

echo Iniciando compilação...
echo.

REM Executar build
call gradlew.bat build

if %errorlevel% neq 0 (
    echo.
    echo [ERRO] Falha na compilação!
    echo Verifique os erros acima e tente novamente.
    pause
    exit /b 1
)

echo.
echo ========================================
echo        Compilação Concluída!
echo ========================================
echo.
echo O arquivo do mod foi criado em:
echo build\libs\terreno-buyer-1.0.0.jar
echo.
echo Para instalar:
echo 1. Copie o arquivo para a pasta mods do Minecraft
echo 2. Certifique-se de ter Fabric API e Meteor Client instalados
echo 3. Inicie o Minecraft com o perfil Fabric 1.20.4
echo.
pause
