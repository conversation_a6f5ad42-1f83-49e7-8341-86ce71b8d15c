# Exemplo de Uso - Terreno Buyer Mod

## Cenário: Comprando uma Proteção no armamc.com

### Passo 1: Preparação
```
[Jogador] Entra no servidor armamc.com
[Jogador] Pega uma vareta (/wand ou item específico do servidor)
[Jogador] Ativa o Terreno Buyer no Meteor Client
```

### Passo 2: Detecção da Proteção
```
[Jogador] Clica com a vareta em uma proteção
[Chat] "Proteção #700786 - Proprietário: [Vazio]"
[Mod] Detecta o número da proteção: 700786
[Mod] "[Terreno Buyer] Proteção #700786 detectada. Aguardando opção de compra..."
[Mod] Inicia auto-clique a cada 1 segundo
```

### Passo 3: Auto-clique Ativo
```
[Mod] Clica automaticamente com botão direito
[Chat] "Esta proteção não está disponível para compra"
[Mod] Continua clicando...
[Chat] "Esta proteção não está disponível para compra"
[Mod] Continua clicando...
```

### Passo 4: Proteção Disponível
```
[Mod] Clica automaticamente com botão direito
[Chat] "Esta proteção está disponível para compra!"
[Chat] "[Comprar Proteção] - Clique para comprar por R$ 50.000"
[Mod] Detecta a mensagem "[Comprar Proteção]"
[Mod] "[Terreno Buyer] Comprando proteção #700786..."
[Mod] Executa: /imob comprar 700786
```

### Passo 5: Resultado
```
[Chat] "Você comprou a proteção #700786 por R$ 50.000!"
[Mod] Para o auto-clique
[Mod] Reseta para próxima compra
```

## Fluxo Técnico

### 1. Detecção de Mensagem de Proteção
```java
// Regex que detecta: "Proteção #700786"
Pattern PROTECTION_PATTERN = Pattern.compile("Proteção #(\\d+)");

// Quando detectado:
currentProtectionId = "700786";
isWaitingForBuyOption = true;
isAutoClicking = true;
```

### 2. Sistema de Auto-clique
```java
// A cada tick (50ms):
if (isAutoClicking && tickCounter >= clickDelay) {
    performRightClick(); // Simula clique direito
    tickCounter = 0;
}
```

### 3. Detecção de Opção de Compra
```java
// Quando detecta "[Comprar Proteção]" no chat:
if (message.contains("[Comprar Proteção]")) {
    buyProtection(currentProtectionId); // Executa /imob comprar 700786
    reset(); // Para o processo
}
```

## Configurações do Meteor Client

### Interface no Meteor Client
```
┌─ Terreno Buyer ─────────────────┐
│ ☑ Terreno Buyer        [ATIVO] │
│                                 │
│ ☑ Auto-click                    │
│ Click Delay: [20] ticks         │
│ ☑ Auto Buy                      │
│ ☑ Notifications                 │
└─────────────────────────────────┘
```

### Configurações Explicadas
- **Auto-click**: Se deve clicar automaticamente
- **Click Delay**: Intervalo entre cliques (20 ticks = 1 segundo)
- **Auto Buy**: Se deve comprar automaticamente
- **Notifications**: Se deve mostrar mensagens no chat

## Mensagens do Mod

### Mensagens de Status
```
[Terreno Buyer] Terreno Buyer ativado!
[Terreno Buyer] Proteção #700786 detectada. Aguardando opção de compra...
[Terreno Buyer] Auto-clique ativado para proteção #700786
[Terreno Buyer] Comprando proteção #700786...
[Terreno Buyer] Auto-clique desativado
[Terreno Buyer] Terreno Buyer desativado!
```

### Logs do Console
```
[INFO] Terreno Buyer mod inicializado!
[INFO] Conectado ao servidor - Terreno Buyer ativo
[INFO] Inicializando Terreno Buyer Addon para Meteor Client
[INFO] Desconectado do servidor - Terreno Buyer pausado
```

## Casos de Uso Avançados

### Múltiplas Proteções
```
1. Compra proteção #700786
2. Vai para próxima proteção
3. Clica na nova proteção
4. Mod detecta proteção #700787
5. Repete o processo automaticamente
```

### Proteções Indisponíveis
```
1. Clica na proteção #700788
2. Mod detecta e inicia auto-clique
3. Proteção nunca fica disponível
4. Jogador pode desativar manualmente ou ir para outra proteção
```

### Configuração Personalizada
```
1. Jogador quer cliques mais rápidos: Define Click Delay para 10 ticks
2. Jogador quer apenas detecção: Desativa Auto Buy
3. Jogador quer silencioso: Desativa Notifications
```

## Comandos Relacionados

### Comandos do Servidor (armamc.com)
```
/wand                    - Pega a vareta de proteção
/imob comprar [número]   - Compra uma proteção (executado pelo mod)
/imob info [número]      - Informações da proteção
/money                   - Ver saldo atual
```

### Teclas do Meteor Client
```
Right Shift             - Abrir/fechar Meteor Client
Clique na proteção      - Ativar detecção do mod
```

## Troubleshooting

### Mod não detecta proteção
- Verifique se está usando a vareta correta
- Certifique-se de que o módulo está ativo
- Verifique se está no servidor armamc.com

### Auto-clique não funciona
- Verifique a configuração "Auto-click"
- Ajuste o "Click Delay" se necessário
- Certifique-se de que há uma proteção detectada

### Compra não acontece
- Verifique a configuração "Auto Buy"
- Certifique-se de que tem dinheiro suficiente
- Verifique se a proteção realmente está disponível
