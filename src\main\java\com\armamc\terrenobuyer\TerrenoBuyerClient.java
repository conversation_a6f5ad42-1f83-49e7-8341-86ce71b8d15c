package com.armamc.terrenobuyer;

import net.fabricmc.api.ClientModInitializer;
import net.fabricmc.fabric.api.client.event.lifecycle.v1.ClientTickEvents;
import net.fabricmc.fabric.api.client.networking.v1.ClientPlayConnectionEvents;
import net.minecraft.client.MinecraftClient;
import net.minecraft.text.Text;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class TerrenoBuyerClient implements ClientModInitializer {
    public static final String MOD_ID = "terreno-buyer";
    public static final Logger LOGGER = LoggerFactory.getLogger(MOD_ID);
    
    private static TerrenoBuyerClient INSTANCE;
    private TerrenoManager terrenoManager;
    
    @Override
    public void onInitializeClient() {
        INSTANCE = this;
        terrenoManager = new TerrenoManager();
        
        LOGGER.info("Terreno Buyer mod inicializado!");
        
        // Registrar eventos
        ClientTickEvents.END_CLIENT_TICK.register(client -> {
            if (client.player != null && client.world != null) {
                terrenoManager.tick();
            }
        });
        
        ClientPlayConnectionEvents.JOIN.register((handler, sender, client) -> {
            terrenoManager.reset();
            LOGGER.info("Conectado ao servidor - Terreno Buyer ativo");
        });
        
        ClientPlayConnectionEvents.DISCONNECT.register((handler, client) -> {
            terrenoManager.reset();
            LOGGER.info("Desconectado do servidor - Terreno Buyer pausado");
        });
    }
    
    public static TerrenoBuyerClient getInstance() {
        return INSTANCE;
    }
    
    public TerrenoManager getTerrenoManager() {
        return terrenoManager;
    }
    
    public static void sendMessage(String message) {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.player != null) {
            client.player.sendMessage(Text.literal("[Terreno Buyer] " + message), false);
        }
    }
}
