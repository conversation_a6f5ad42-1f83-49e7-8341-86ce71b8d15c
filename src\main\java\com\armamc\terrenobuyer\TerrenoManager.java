package com.armamc.terrenobuyer;

import com.armamc.terrenobuyer.modules.TerrenoBuyerModule;
import meteordevelopment.meteorclient.systems.modules.Modules;
import net.minecraft.client.MinecraftClient;
import net.minecraft.util.Hand;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.hit.HitResult;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class TerrenoManager {
    private static final Pattern PROTECTION_PATTERN = Pattern.compile("Proteção #(\\d+)");
    private static final String BUY_PROTECTION_TEXT = "[Comprar Proteção]";
    
    private String currentProtectionId = null;
    private boolean isWaitingForBuyOption = false;
    private boolean isAutoClicking = false;
    private int tickCounter = 0;
    private static final int CLICK_INTERVAL = 20; // 1 segundo (20 ticks)
    
    private final MinecraftClient client;
    
    public TerrenoManager() {
        this.client = MinecraftClient.getInstance();
    }
    
    public void tick() {
        TerrenoBuyerModule module = Modules.get().get(TerrenoBuyerModule.class);
        if (module == null || !module.shouldAutoClick() || !isAutoClicking) return;

        tickCounter++;
        int interval = module.getClickDelay();
        if (tickCounter >= interval) {
            tickCounter = 0;
            performRightClick();
        }
    }
    
    public void onChatMessage(String message) {
        // Limpar a mensagem de formatação do Minecraft
        String cleanMessage = message.replaceAll("§[0-9a-fk-or]", "");

        // Verificar se é uma mensagem de proteção
        Matcher protectionMatcher = PROTECTION_PATTERN.matcher(cleanMessage);
        if (protectionMatcher.find()) {
            currentProtectionId = protectionMatcher.group(1);
            isWaitingForBuyOption = true;
            isAutoClicking = true;

            TerrenoBuyerModule module = Modules.get().get(TerrenoBuyerModule.class);
            if (module == null || module.shouldShowNotifications()) {
                TerrenoBuyerClient.sendMessage("Proteção #" + currentProtectionId + " detectada. Aguardando opção de compra...");
            }
            return;
        }

        // Verificar se apareceu a opção de comprar
        if (isWaitingForBuyOption && cleanMessage.contains(BUY_PROTECTION_TEXT)) {
            TerrenoBuyerModule module = Modules.get().get(TerrenoBuyerModule.class);
            if (currentProtectionId != null && (module == null || module.shouldAutoBuy())) {
                buyProtection(currentProtectionId);
                reset();
            }
        }
    }
    
    private void performRightClick() {
        if (client.player == null || client.world == null) return;
        
        // Simular clique com botão direito
        HitResult hitResult = client.crosshairTarget;
        if (hitResult != null && hitResult.getType() == HitResult.Type.BLOCK) {
            BlockHitResult blockHitResult = (BlockHitResult) hitResult;
            
            // Simular interação com o bloco
            if (client.interactionManager != null) {
                client.interactionManager.interactBlock(
                    client.player,
                    Hand.MAIN_HAND,
                    blockHitResult
                );
            }
        } else {
            // Se não há bloco, simular clique no ar
            if (client.interactionManager != null) {
                client.interactionManager.interactItem(client.player, Hand.MAIN_HAND);
            }
        }
    }
    
    private void buyProtection(String protectionId) {
        if (client.player == null) return;

        String command = "/imob comprar " + protectionId;

        TerrenoBuyerModule module = Modules.get().get(TerrenoBuyerModule.class);
        if (module == null || module.shouldShowNotifications()) {
            TerrenoBuyerClient.sendMessage("Comprando proteção #" + protectionId + "...");
        }

        // Enviar comando
        if (client.getNetworkHandler() != null) {
            client.getNetworkHandler().sendChatCommand(command.substring(1)); // Remove a barra do comando
        }
    }
    
    public void reset() {
        currentProtectionId = null;
        isWaitingForBuyOption = false;
        isAutoClicking = false;
        tickCounter = 0;
    }
    
    public void startAutoClick() {
        if (currentProtectionId != null) {
            isAutoClicking = true;
            TerrenoBuyerClient.sendMessage("Auto-clique ativado para proteção #" + currentProtectionId);
        }
    }
    
    public void stopAutoClick() {
        isAutoClicking = false;
        TerrenoBuyerClient.sendMessage("Auto-clique desativado");
    }
    
    public boolean isAutoClicking() {
        return isAutoClicking;
    }
    
    public String getCurrentProtectionId() {
        return currentProtectionId;
    }
}
