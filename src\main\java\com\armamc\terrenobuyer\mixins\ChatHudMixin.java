package com.armamc.terrenobuyer.mixins;

import com.armamc.terrenobuyer.TerrenoBuyerClient;
import net.minecraft.client.gui.hud.ChatHud;
import net.minecraft.text.Text;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

@Mixin(ChatHud.class)
public class ChatHudMixin {
    
    @Inject(method = "addMessage(Lnet/minecraft/text/Text;)V", at = @At("HEAD"))
    private void onChatMessage(Text message, CallbackInfo ci) {
        if (TerrenoBuyerClient.getInstance() != null) {
            String messageString = message.getString();
            TerrenoBuyerClient.getInstance().getTerrenoManager().onChatMessage(messageString);
        }
    }
}
